const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const path = require('path');
const fs = require('fs-extra');
require('dotenv').config();

// Import database connection
const connectDB = require('./config/database');

// Import models
const User = require('./models/User');
const Room = require('./models/Room');
const Message = require('./models/Message');

// Import middleware
const { authenticateSocket, createRateLimit } = require('./middleware/auth');

// Import utilities
const { franzetta, decryptFranzetta, generateToken, hashPassword, verifyPassword, generateFingerprintHash } = require('./utils/encryption');

// Initialize Express app
const app = express();
const server = http.createServer(app);

// Initialize Socket.IO with CORS and proper configuration for frontend compatibility
const io = socketIo(server, {
  cors: {
    origin: process.env.SOCKET_CORS_ORIGIN || "*",
    methods: process.env.SOCKET_CORS_METHODS?.split(',') || ["GET", "POST"],
    credentials: true
  },
  path: '/socket.io/',
  transports: ['websocket', 'polling'],
  allowEIO3: true, // Allow Engine.IO v3 for compatibility
  pingTimeout: 60000,
  pingInterval: 25000
});

// Intercept WebSocket upgrade requests to handle raw WebSocket connections
server.on('upgrade', (request, socket, head) => {
  const url = new URL(request.url, `http://${request.headers.host}`);

  // Check if this is a raw WebSocket connection (EIO=4&transport=websocket)
  if (url.pathname === '/socket.io/' && url.searchParams.get('EIO') === '4' && url.searchParams.get('transport') === 'websocket') {
    console.log('Intercepting raw WebSocket connection');

    // Handle this as a raw WebSocket connection
    const WebSocket = require('ws');
    const wss = new WebSocket.Server({ noServer: true });

    wss.handleUpgrade(request, socket, head, (ws) => {
      console.log('Raw WebSocket connection established');

      // Create a socket wrapper that mimics Socket.IO interface
      const socketWrapper = {
        id: generateToken(),
        send: (data) => {
          if (ws.readyState === WebSocket.OPEN) {
            ws.send(data);
          }
        },
        emit: (event, data) => {
          // For raw WebSocket, we send the data directly as JSON
          if (ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify(data));
          }
        },
        handshake: {
          address: request.socket.remoteAddress,
          headers: request.headers
        }
      };

      // Store connection
      activeConnections.set(socketWrapper.id, {
        socket: socketWrapper,
        user: null,
        room: null,
        joinedAt: new Date(),
        isRawWebSocket: true
      });

      // Handle messages
      ws.on('message', async (data) => {
        try {
          console.log('Raw WebSocket data received:', data.toString());
          const message = JSON.parse(data.toString());
          console.log('Parsed message:', message);

          // Handle handshake
          if (message.cmd === 'hi') {
            const response = {
              cmd: 'hi',
              data: franzetta('handshake_success')
            };
            console.log('Sending handshake response:', response);
            ws.send(JSON.stringify(response));
            return;
          }

          // Decrypt command if encrypted
          let cmd = message.cmd;
          if (cmd && typeof cmd === 'string') {
            try {
              cmd = decryptFranzetta(cmd);
            } catch (e) {
              // Command might not be encrypted
            }
          }

          // Handle different commands
          await handleSocketCommand(socketWrapper, cmd, message.data || message);

        } catch (error) {
          console.error('Raw WebSocket message error:', error);
          console.error('Raw data was:', data.toString());
        }
      });

      // Handle disconnect
      ws.on('close', async () => {
        console.log('Raw WebSocket disconnected');
        await handleDisconnect(socketWrapper);
      });

      // Handle errors
      ws.on('error', (error) => {
        console.error('Raw WebSocket error:', error);
      });
    });
  } else {
    // Let Socket.IO handle other connections
    io.engine.handleUpgrade(request, socket, head);
  }
});

// Connect to database
connectDB();

// Middleware setup
app.use(helmet({
  contentSecurityPolicy: false,
  crossOriginEmbedderPolicy: false
}));
app.use(compression());
app.use(morgan('combined'));
app.use(cors({
  origin: process.env.CORS_ORIGIN || "*",
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Rate limiting
app.use(createRateLimit(
  parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000,
  parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100
));

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// Ensure upload directory exists
const uploadDir = path.join(__dirname, 'public', 'uploads');
fs.ensureDirSync(uploadDir);

// API routes
const apiRoutes = require('./routes/api');
app.use('/api', apiRoutes);

// Handle file uploads (legacy endpoints for frontend compatibility)
app.post('/upload', apiRoutes);
app.post('/pic', apiRoutes);

// Root endpoint - serve index.html
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Status endpoint
app.get('/status', (req, res) => {
  res.json({
    status: 'online',
    server: 'Chat Backend Server',
    version: '1.0.0',
    timestamp: new Date(),
    uptime: process.uptime(),
    connections: activeConnections.size,
    memory: process.memoryUsage(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Store active connections
const activeConnections = new Map();
const roomConnections = new Map();

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('New socket connection:', socket.id);

  // Store connection
  activeConnections.set(socket.id, {
    socket: socket,
    user: null,
    room: null,
    joinedAt: new Date()
  });

  // Handle Socket.IO messages
  socket.on('message', async (data) => {
    try {
      console.log('Socket.IO message received:', data);

      let message;
      if (typeof data === 'string') {
        message = JSON.parse(data);
      } else {
        message = data;
      }

      // Handle Socket.IO v4 handshake
      if (message.cmd === 'hi') {
        sendMessage(socket, 'hi', franzetta('handshake_success'));
        return;
      }

      // Decrypt command if encrypted
      let cmd = message.cmd;
      if (cmd && typeof cmd === 'string') {
        try {
          cmd = decryptFranzetta(cmd);
        } catch (e) {
          // Command might not be encrypted
        }
      }

      // Handle different commands
      await handleSocketCommand(socket, cmd, message.data || message);

    } catch (error) {
      console.error('Socket.IO message error:', error);
    }
  });

  // Handle disconnect
  socket.on('disconnect', async () => {
    console.log('Socket disconnected:', socket.id);
    await handleDisconnect(socket);
  });

  // Handle errors
  socket.on('error', (error) => {
    console.error('Socket error:', error);
  });
});

// Handle socket commands
async function handleSocketCommand(socket, cmd, data) {
  const connection = activeConnections.get(socket.id);
  
  try {
    switch (cmd) {
      case 'online':
        await handleOnline(socket, data);
        break;
        
      case 'g':
        await handleGuestLogin(socket, data);
        break;
        
      case 'login':
        await handleUserLogin(socket, data);
        break;
        
      case 'reg':
        await handleUserRegistration(socket, data);
        break;
        
      case 'ping':
        await handlePing(socket, data);
        break;
        
      case 'bc':
        await handleBroadcastMessage(socket, data);
        break;
        
      case 'msg':
        await handleMessage(socket, data);
        break;
        
      case 'pm':
        await handlePrivateMessage(socket, data);
        break;
        
      case 'rjoin':
        await handleRoomJoin(socket, data);
        break;
        
      case 'rleave':
        await handleRoomLeave(socket, data);
        break;
        
      case 'logout':
        await handleLogout(socket, data);
        break;
        
      default:
        console.log('Unknown command:', cmd);
        break;
    }
  } catch (error) {
    console.error(`Error handling command ${cmd}:`, error);
    socket.emit('error', { message: 'Command processing failed' });
  }
}

// Send message in the format expected by frontend
function sendMessage(socket, cmd, data) {
  const message = {
    cmd: cmd,
    data: data
  };

  try {
    // Check if this is a raw WebSocket connection
    const connection = activeConnections.get(socket.id);
    if (connection && connection.isRawWebSocket) {
      // For raw WebSocket, send directly as JSON
      socket.send(JSON.stringify(message));
    } else {
      // Use Socket.IO's emit method to send the message
      socket.emit('message', message);
    }
  } catch (error) {
    console.error('Error sending message:', error);
  }
}

// Handle online status
async function handleOnline(socket, data) {
  sendMessage(socket, franzetta('server'), { online: 'متصل بالخادم' });
}

// Handle guest login
async function handleGuestLogin(socket, data) {
  try {
    const { username, fp, refr, r } = data;
    
    if (!username || !fp) {
      sendMessage(socket, franzetta('login'), { msg: 'error', error: 'اسم المستخدم مطلوب' });
      return;
    }

    // Check if username is already taken
    const existingUser = await User.findOne({ username: username.trim() });
    if (existingUser && !existingUser.isGuest) {
      sendMessage(socket, franzetta('login'), { msg: 'error', error: 'اسم المستخدم محجوز' });
      return;
    }

    // Create or update guest user
    const fingerprintHash = generateFingerprintHash(fp);
    const ipAddress = socket.handshake.address;
    
    let user = await User.findOne({ 
      'security.fingerprint': fingerprintHash,
      isGuest: true 
    });

    if (!user) {
      user = new User({
        username: username.trim(),
        isGuest: true,
        isOnline: true,
        security: {
          fingerprint: fingerprintHash,
          ipAddress: ipAddress,
          userAgent: socket.handshake.headers['user-agent'] || ''
        }
      });
      await user.save();
    } else {
      user.username = username.trim();
      user.isOnline = true;
      user.stats.lastSeen = new Date();
      await user.save();
    }

    // Update connection
    const connection = activeConnections.get(socket.id);
    connection.user = user;

    // Send success response
    sendMessage(socket, franzetta('login'), {
      msg: 'ok',
      user: user.getPublicProfile(),
      token: generateToken()
    });

    console.log(`Guest ${username} connected`);

  } catch (error) {
    console.error('Guest login error:', error);
    sendMessage(socket, franzetta('login'), { msg: 'error', error: 'خطأ في الخادم' });
  }
}

// Handle user login
async function handleUserLogin(socket, data) {
  try {
    const { username, password, stealth, fp, refr, r } = data;

    if (!username || !password || !fp) {
      sendMessage(socket, franzetta('login'), { msg: 'error', error: 'اسم المستخدم وكلمة المرور مطلوبان' });
      return;
    }

    // Find user
    const user = await User.findOne({ username: username.trim() });
    if (!user || user.isGuest) {
      sendMessage(socket, franzetta('login'), { msg: 'error', error: 'اسم المستخدم غير موجود' });
      return;
    }

    // Check if account is locked
    if (user.isLocked) {
      sendMessage(socket, franzetta('login'), { msg: 'error', error: 'الحساب مقفل مؤقتاً' });
      return;
    }

    // Verify password
    const isValidPassword = await user.comparePassword(password);
    if (!isValidPassword) {
      await user.incLoginAttempts();
      sendMessage(socket, franzetta('login'), { msg: 'error', error: 'كلمة المرور خاطئة' });
      return;
    }

    // Reset login attempts on successful login
    if (user.security.loginAttempts > 0) {
      await user.resetLoginAttempts();
    }

    // Update user status
    user.isOnline = true;
    user.stats.lastSeen = new Date();
    user.settings.stealth = stealth || false;
    user.security.fingerprint = generateFingerprintHash(fp);
    user.security.ipAddress = socket.handshake.address;
    await user.save();

    // Update connection
    const connection = activeConnections.get(socket.id);
    connection.user = user;

    // Send success response
    sendMessage(socket, franzetta('login'), {
      msg: 'ok',
      user: user.getPublicProfile(),
      token: generateToken()
    });

    console.log(`User ${username} logged in`);

  } catch (error) {
    console.error('User login error:', error);
    sendMessage(socket, franzetta('login'), { msg: 'error', error: 'خطأ في الخادم' });
  }
}

// Handle user registration
async function handleUserRegistration(socket, data) {
  try {
    const { username, password, fp, refr, r } = data;

    if (!username || !password || !fp) {
      sendMessage(socket, franzetta('reg'), { msg: 'error', error: 'جميع الحقول مطلوبة' });
      return;
    }

    if (password.length < 6) {
      sendMessage(socket, franzetta('reg'), { msg: 'error', error: 'كلمة المرور قصيرة جداً' });
      return;
    }

    // Check if username is already taken
    const existingUser = await User.findOne({ username: username.trim() });
    if (existingUser) {
      sendMessage(socket, franzetta('reg'), { msg: 'error', error: 'اسم المستخدم محجوز' });
      return;
    }

    // Create new user
    const fingerprintHash = generateFingerprintHash(fp);
    const user = new User({
      username: username.trim(),
      password: password,
      isGuest: false,
      isOnline: true,
      security: {
        fingerprint: fingerprintHash,
        ipAddress: socket.handshake.address,
        userAgent: socket.handshake.headers['user-agent'] || ''
      }
    });

    await user.save();

    // Update connection
    const connection = activeConnections.get(socket.id);
    connection.user = user;

    // Send success response
    sendMessage(socket, franzetta('reg'), {
      msg: 'ok',
      user: user.getPublicProfile(),
      token: generateToken()
    });

    console.log(`New user ${username} registered`);

  } catch (error) {
    console.error('User registration error:', error);
    sendMessage(socket, franzetta('reg'), { msg: 'error', error: 'خطأ في الخادم' });
  }
}

// Handle ping
async function handlePing(socket, data) {
  sendMessage(socket, franzetta('pong'), data);
}

// Handle broadcast message
async function handleBroadcastMessage(socket, data) {
  try {
    const connection = activeConnections.get(socket.id);
    if (!connection || !connection.user) {
      return;
    }

    const { msg, link, bid } = data;
    if (!msg || msg.trim() === '') {
      return;
    }

    // Create message
    const message = new Message({
      content: msg.trim(),
      type: link ? 'file' : 'text',
      sender: connection.user._id,
      senderName: connection.user.username,
      room: connection.room,
      attachment: link ? { url: link } : undefined,
      replyTo: bid || undefined,
      metadata: {
        ipAddress: socket.handshake.address,
        fingerprint: connection.user.security.fingerprint
      }
    });

    await message.save();

    // Broadcast to room or all users
    const broadcastData = {
      cmd: franzetta('bc'),
      data: {
        msg: msg,
        user: connection.user.getPublicProfile(),
        time: new Date(),
        id: message._id
      }
    };

    if (connection.room) {
      // Broadcast to room
      socket.to(connection.room).emit('message', JSON.stringify(broadcastData));
    } else {
      // Broadcast to all
      socket.broadcast.emit('message', JSON.stringify(broadcastData));
    }

    console.log(`Broadcast from ${connection.user.username}: ${msg}`);

  } catch (error) {
    console.error('Broadcast message error:', error);
  }
}

// Handle regular message
async function handleMessage(socket, data) {
  try {
    const connection = activeConnections.get(socket.id);
    if (!connection || !connection.user || !connection.room) {
      return;
    }

    const { msg, mi } = data;
    if (!msg || msg.trim() === '') {
      return;
    }

    // Create message
    const message = new Message({
      content: msg.trim(),
      type: 'text',
      sender: connection.user._id,
      senderName: connection.user.username,
      room: connection.room,
      replyTo: mi || undefined,
      metadata: {
        ipAddress: socket.handshake.address,
        fingerprint: connection.user.security.fingerprint
      }
    });

    await message.save();

    // Broadcast to room
    const broadcastData = {
      cmd: franzetta('msg'),
      data: {
        msg: msg,
        user: connection.user.getPublicProfile(),
        time: new Date(),
        id: message._id,
        room: connection.room
      }
    };

    socket.to(connection.room).emit('message', JSON.stringify(broadcastData));

  } catch (error) {
    console.error('Message error:', error);
  }
}

// Handle private message
async function handlePrivateMessage(socket, data) {
  try {
    const connection = activeConnections.get(socket.id);
    if (!connection || !connection.user) {
      return;
    }

    const { msg, id } = data;
    if (!msg || !id || msg.trim() === '') {
      return;
    }

    // Find recipient
    const recipient = await User.findById(id);
    if (!recipient) {
      return;
    }

    // Create private message
    const message = new Message({
      content: msg.trim(),
      type: 'text',
      sender: connection.user._id,
      senderName: connection.user.username,
      recipient: recipient._id,
      isPrivate: true,
      metadata: {
        ipAddress: socket.handshake.address,
        fingerprint: connection.user.security.fingerprint
      }
    });

    await message.save();

    // Find recipient's socket
    const recipientConnection = Array.from(activeConnections.values())
      .find(conn => conn.user && conn.user._id.toString() === id);

    if (recipientConnection) {
      const pmData = {
        cmd: franzetta('pm'),
        data: {
          msg: msg,
          sender: connection.user.getPublicProfile(),
          time: new Date(),
          id: message._id
        }
      };
      recipientConnection.socket.emit('message', JSON.stringify(pmData));
    }

  } catch (error) {
    console.error('Private message error:', error);
  }
}

// Handle room join
async function handleRoomJoin(socket, data) {
  try {
    const connection = activeConnections.get(socket.id);
    if (!connection || !connection.user) {
      return;
    }

    const { id, pwd } = data;
    if (!id) {
      return;
    }

    // Find room
    const room = await Room.findById(id);
    if (!room || !room.isActive) {
      sendMessage(socket, franzetta('rjoin'), { msg: 'error', error: 'الغرفة غير موجودة' });
      return;
    }

    // Check password if required
    if (room.settings.password && room.settings.password !== pwd) {
      sendMessage(socket, franzetta('rjoin'), { msg: 'error', error: 'كلمة مرور خاطئة' });
      return;
    }

    // Check if user is banned
    if (room.isUserBanned(connection.user._id)) {
      sendMessage(socket, franzetta('rjoin'), { msg: 'error', error: 'أنت محظور من هذه الغرفة' });
      return;
    }

    // Leave current room if any
    if (connection.room) {
      socket.leave(connection.room);
    }

    // Join new room
    const joined = room.addUser(connection.user._id);
    if (!joined) {
      sendMessage(socket, franzetta('rjoin'), { msg: 'error', error: 'الغرفة ممتلئة' });
      return;
    }

    await room.save();

    // Update connection
    connection.room = room._id.toString();
    socket.join(room._id.toString());

    // Send success response
    sendMessage(socket, franzetta('rjoin'), {
      msg: 'ok',
      room: room.getRoomInfo()
    });

    // Notify room of new user
    socket.to(room._id.toString()).emit('message', JSON.stringify({
      cmd: franzetta('ujoin'),
      data: {
        user: connection.user.getPublicProfile(),
        room: room._id
      }
    }));

  } catch (error) {
    console.error('Room join error:', error);
  }
}

// Handle room leave
async function handleRoomLeave(socket, data) {
  try {
    const connection = activeConnections.get(socket.id);
    if (!connection || !connection.user || !connection.room) {
      return;
    }

    // Find room
    const room = await Room.findById(connection.room);
    if (room) {
      room.removeUser(connection.user._id);
      await room.save();

      // Notify room of user leaving
      socket.to(connection.room).emit('message', JSON.stringify({
        cmd: franzetta('uleave'),
        data: {
          user: connection.user.getPublicProfile(),
          room: room._id
        }
      }));
    }

    // Leave room
    socket.leave(connection.room);
    connection.room = null;

  } catch (error) {
    console.error('Room leave error:', error);
  }
}

// Handle logout
async function handleLogout(socket, data) {
  try {
    const connection = activeConnections.get(socket.id);
    if (connection && connection.user) {
      // Update user status
      connection.user.isOnline = false;
      connection.user.stats.lastSeen = new Date();
      await connection.user.save();

      // Leave room if in one
      if (connection.room) {
        await handleRoomLeave(socket, {});
      }
    }

    // Clear connection
    activeConnections.delete(socket.id);

  } catch (error) {
    console.error('Logout error:', error);
  }
}

// Handle disconnect
async function handleDisconnect(socket) {
  try {
    const connection = activeConnections.get(socket.id);
    if (connection && connection.user) {
      // Update user status
      connection.user.isOnline = false;
      connection.user.stats.lastSeen = new Date();
      await connection.user.save();

      // Leave room if in one
      if (connection.room) {
        await handleRoomLeave(socket, {});
      }
    }

    // Clear connection
    activeConnections.delete(socket.id);

  } catch (error) {
    console.error('Disconnect error:', error);
  }
}

// Start server
const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
});
