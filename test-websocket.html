<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test</title>
</head>
<body>
    <h1>WebSocket Connection Test</h1>
    <div id="status">Connecting...</div>
    <div id="messages"></div>
    
    <script>
        const statusDiv = document.getElementById('status');
        const messagesDiv = document.getElementById('messages');
        
        function log(message) {
            console.log(message);
            messagesDiv.innerHTML += '<div>' + message + '</div>';
        }
        
        // Test Socket.IO connection
        log('Testing Socket.IO connection...');
        
        try {
            // Try to connect using Socket.IO client
            const script = document.createElement('script');
            script.src = '/socket.io/socket.io.js';
            script.onload = function() {
                log('Socket.IO client loaded');
                const socket = io();
                
                socket.on('connect', function() {
                    log('✅ Socket.IO connected: ' + socket.id);
                    statusDiv.textContent = 'Connected via Socket.IO';
                    
                    // Test sending a message
                    socket.emit('message', {cmd: 'hi', data: 'test'});
                });
                
                socket.on('disconnect', function() {
                    log('❌ Socket.IO disconnected');
                    statusDiv.textContent = 'Disconnected';
                });
                
                socket.on('message', function(data) {
                    log('📨 Received message: ' + JSON.stringify(data));
                });
                
                socket.on('error', function(error) {
                    log('⚠️ Socket.IO error: ' + error);
                });
            };
            
            script.onerror = function() {
                log('❌ Failed to load Socket.IO client, trying raw WebSocket...');
                testRawWebSocket();
            };
            
            document.head.appendChild(script);
            
        } catch (error) {
            log('❌ Socket.IO test failed: ' + error);
            testRawWebSocket();
        }
        
        function testRawWebSocket() {
            log('Testing raw WebSocket connection...');
            
            const ws = new WebSocket('ws://localhost:3000/socket.io/?EIO=4&transport=websocket');
            
            ws.onopen = function() {
                log('✅ Raw WebSocket connected');
                statusDiv.textContent = 'Connected via Raw WebSocket';
                
                // Send test message
                ws.send(JSON.stringify({cmd: 'hi', data: 'test'}));
            };
            
            ws.onmessage = function(event) {
                log('📨 Raw WebSocket message: ' + event.data);
            };
            
            ws.onerror = function(error) {
                log('⚠️ Raw WebSocket error: ' + error);
            };
            
            ws.onclose = function() {
                log('❌ Raw WebSocket closed');
                statusDiv.textContent = 'Disconnected';
            };
        }
    </script>
</body>
</html>
