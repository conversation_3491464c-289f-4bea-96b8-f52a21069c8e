{"name": "chat-backend", "version": "1.0.0", "description": "Backend server for chat application with WebSocket support", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "setup": "node start.js", "init-db": "node scripts/init-db.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["chat", "websocket", "socket.io", "mongodb", "express"], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "chat-backend": "file:", "compression": "^1.7.4", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "fs-extra": "^11.2.0", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "path": "^0.12.7", "socket.io": "^4.7.4", "uuid": "^9.0.1", "ws": "^8.18.2"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}}